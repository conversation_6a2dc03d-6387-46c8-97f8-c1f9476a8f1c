<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>database_viewer</class>
 <widget class="QWidget" name="database_viewer">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1269</width>
    <height>250</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Database Viewer</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_1">
     <item>
      <widget class="QPushButton" name="open_db_btn">
       <property name="text">
        <string>打开数据库文件</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="db_path_line_edit">
       <property name="readOnly">
        <bool>true</bool>
       </property>
       <property name="placeholderText">
        <string>未加载数据库文件</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>数据表:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="table_selector_combo"/>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string>自定义查询</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QTextEdit" name="sql_query_textedit">
        <property name="placeholderText">
         <string>在此输入 SELECT 查询语句...</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="execute_query_btn">
          <property name="text">
           <string>执行查询</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>查询结果:</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QTableView" name="results_tableview"/>
   </item>
   <item>
    <widget class="QLabel" name="status_label">
     <property name="text">
      <string>状态: 未连接</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
