r'''
Description: 
Author: li
Date: 2025-09-21 16:15:24
LastEditors: li
LastEditTime: 2025-09-27 21:28:35
FilePath: \\单三相电表测试软件\\software\\multi_task_backend.py
'''

import sys
import time
import signal
from PySide6.QtCore import QCoreApplication, QThread, QObject, Signal, Slot, QTimer

# ==============================================================================
#   通用后台任务工作器 (Worker)
# ==============================================================================
class Worker(QObject):
    """
    一个通用的工作器，可以被移动到任何 QThread 中执行。
    它包含一个循环，在指定的时间间隔内执行操作。
    """
    # 任务完成时发射的信号
    finished = Signal()

    def __init__(self, task_name: str, interval_ms: int):
        super().__init__()
        self.task_name = task_name
        self.interval = interval_ms
        self._is_running = True

    @Slot() # 这个槽函数会在工作器的线程中执行
    def run(self):
        """
        任务的主循环。线程启动后会自动调用此方法。
        """
        # QThread.currentThread() 返回线程对象的指针/引用，可以作为唯一标识
        thread_id = QThread.currentThread()
        print(f"任务 [{self.task_name}] 已在线程 {thread_id} 中启动。")
        while self._is_running:
            print(f"DEBUG: 任务 [{self.task_name}] 正在运行... (线程ID: {thread_id})")
            time.sleep(self.interval / 1000)
        
        print(f"任务 [{self.task_name}] 已停止。")
        self.finished.emit()

    def stop(self):
        """
        请求停止任务循环。
        """
        self._is_running = False

# ==============================================================================
#   多任务后台管理器
# ==============================================================================
class MultiTaskBackend(QObject):
    """
    一个纯粹的后台任务管理器，它本身不负责创建或运行Qt应用实例。
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self._threads = {}
        self._workers = {}

    def start_task(self, task_name: str, interval_ms: int):
        """
        创建一个新的工作器和线程，并启动它。
        """
        thread = QThread()
        worker = Worker(task_name, interval_ms)

        self._threads[task_name] = thread
        self._workers[task_name] = worker

        worker.moveToThread(thread) # 将工作器移动到新线程

        thread.started.connect(worker.run) # 连接线程启动信号和工作器的 run 方法
        worker.finished.connect(thread.quit) # 连接工作器完成信号和线程退出方法
        worker.finished.connect(worker.deleteLater) # 连接工作器完成信号和删除工作器方法
        thread.finished.connect(thread.deleteLater) # 连接线程完成信号和删除线程方法

        thread.start() # 启动新线程
        print(f"主线程: 已请求启动任务 [{task_name}]")

    def start_all_tasks(self):
        """
        启动所有预定义的任务。
        """
        print("--- 多任务后台启动 ---")
        self.start_task(task_name="串口数据打包任务 (模拟)", interval_ms=2000)
        self.start_task(task_name="下发升级包任务 (模拟)", interval_ms=5000)
        self.start_task(task_name="645数据处理任务 (模拟)", interval_ms=3000)
        print("--- 所有任务已启动，后台运行中... (按 Ctrl+C 退出) ---")

    def stop_all(self):
        """
        优雅地停止所有正在运行的任务并请求应用退出。
        """
        print("--- 正在停止所有任务... ---")
        for worker in self._workers.values():
            worker.stop()
        
        app = QCoreApplication.instance()
        if app:
            print("--- 请求主程序退出 ---")
            QTimer.singleShot(200, app.quit)

# ==============================================================================
#   主执行入口 (用于独立测试)
# ==============================================================================
if __name__ == "__main__":
    # 1. 首先，创建唯一的 QCoreApplication 实例
    app = QCoreApplication(sys.argv)

    # 2. 然后，创建后台任务管理器
    backend = MultiTaskBackend()

    # 3. 设置信号处理器，以便 Ctrl+C 可以优雅地停止后台
    signal.signal(signal.SIGINT, lambda sig, frame: backend.stop_all())
    
    # 4. 启动所有后台任务
    backend.start_all_tasks()

    # 5. 启动 Qt 的事件循环。程序将在这里阻塞，直到 app.quit() 被调用。
    sys.exit(app.exec())
