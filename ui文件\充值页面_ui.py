# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file '充值页面.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, Q<PERSON><PERSON>alGradient, Q<PERSON>ursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QGridLayout, QLabel, QLineEdit,
    QPushButton, QSizePolicy, QTextEdit, QWidget)

class Ui_payment(object):
    def setupUi(self, payment):
        if not payment.objectName():
            payment.setObjectName(u"payment")
        payment.resize(1146, 310)
        self.gridLayout_2 = QGridLayout(payment)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout = QGridLayout()
        self.gridLayout.setObjectName(u"gridLayout")
        self.label_5 = QLabel(payment)
        self.label_5.setObjectName(u"label_5")
        font = QFont()
        font.setPointSize(11)
        self.label_5.setFont(font)

        self.gridLayout.addWidget(self.label_5, 0, 0, 1, 1)

        self.password_line = QLineEdit(payment)
        self.password_line.setObjectName(u"password_line")
        self.password_line.setFont(font)

        self.gridLayout.addWidget(self.password_line, 0, 1, 1, 2)

        self.new_password_line = QLineEdit(payment)
        self.new_password_line.setObjectName(u"new_password_line")
        self.new_password_line.setFont(font)
        self.new_password_line.setLayoutDirection(Qt.LayoutDirection.LeftToRight)

        self.gridLayout.addWidget(self.new_password_line, 0, 3, 1, 1)

        self.label_2 = QLabel(payment)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setFont(font)

        self.gridLayout.addWidget(self.label_2, 0, 4, 1, 1)

        self.count_code_line = QLineEdit(payment)
        self.count_code_line.setObjectName(u"count_code_line")
        self.count_code_line.setFont(font)

        self.gridLayout.addWidget(self.count_code_line, 0, 5, 1, 1)

        self.label_3 = QLabel(payment)
        self.label_3.setObjectName(u"label_3")
        self.label_3.setFont(font)

        self.gridLayout.addWidget(self.label_3, 0, 6, 1, 1)

        self.pay_much_line = QLineEdit(payment)
        self.pay_much_line.setObjectName(u"pay_much_line")
        self.pay_much_line.setFont(font)

        self.gridLayout.addWidget(self.pay_much_line, 0, 7, 1, 1)

        self.label_4 = QLabel(payment)
        self.label_4.setObjectName(u"label_4")
        self.label_4.setFont(font)

        self.gridLayout.addWidget(self.label_4, 0, 8, 1, 1)

        self.pay_count_line = QLineEdit(payment)
        self.pay_count_line.setObjectName(u"pay_count_line")
        self.pay_count_line.setFont(font)

        self.gridLayout.addWidget(self.pay_count_line, 0, 9, 1, 1)

        self.verify_btn = QPushButton(payment)
        self.verify_btn.setObjectName(u"verify_btn")
        self.verify_btn.setFont(font)

        self.gridLayout.addWidget(self.verify_btn, 1, 0, 1, 2)

        self.textEdit = QTextEdit(payment)
        self.textEdit.setObjectName(u"textEdit")
        self.textEdit.setReadOnly(True)

        self.gridLayout.addWidget(self.textEdit, 1, 2, 8, 8)

        self.dis_verify_btn = QPushButton(payment)
        self.dis_verify_btn.setObjectName(u"dis_verify_btn")
        self.dis_verify_btn.setFont(font)

        self.gridLayout.addWidget(self.dis_verify_btn, 2, 0, 1, 2)

        self.open_account = QPushButton(payment)
        self.open_account.setObjectName(u"open_account")
        self.open_account.setFont(font)

        self.gridLayout.addWidget(self.open_account, 3, 0, 1, 2)

        self.purchase_btn = QPushButton(payment)
        self.purchase_btn.setObjectName(u"purchase_btn")
        self.purchase_btn.setFont(font)

        self.gridLayout.addWidget(self.purchase_btn, 4, 0, 1, 2)

        self.changed_password_btn = QPushButton(payment)
        self.changed_password_btn.setObjectName(u"changed_password_btn")
        self.changed_password_btn.setFont(font)

        self.gridLayout.addWidget(self.changed_password_btn, 5, 0, 1, 2)

        self.check_status_btn = QPushButton(payment)
        self.check_status_btn.setObjectName(u"check_status_btn")
        self.check_status_btn.setFont(font)

        self.gridLayout.addWidget(self.check_status_btn, 6, 0, 1, 2)

        self.close_account_btn = QPushButton(payment)
        self.close_account_btn.setObjectName(u"close_account_btn")
        self.close_account_btn.setFont(font)

        self.gridLayout.addWidget(self.close_account_btn, 7, 0, 1, 2)

        self.refund_btn = QPushButton(payment)
        self.refund_btn.setObjectName(u"refund_btn")
        self.refund_btn.setFont(font)

        self.gridLayout.addWidget(self.refund_btn, 8, 0, 1, 2)


        self.gridLayout_2.addLayout(self.gridLayout, 0, 0, 1, 1)


        self.retranslateUi(payment)

        QMetaObject.connectSlotsByName(payment)
    # setupUi

    def retranslateUi(self, payment):
        payment.setWindowTitle(QCoreApplication.translate("payment", u"\u5145\u503c", None))
        self.label_5.setText(QCoreApplication.translate("payment", u"\u5145\u503c\u5bc6\u7801", None))
        self.password_line.setText(QCoreApplication.translate("payment", u"********", None))
        self.password_line.setPlaceholderText(QCoreApplication.translate("payment", u"#\u5145\u503c\u5bc6\u7801", None))
        self.new_password_line.setText("")
        self.new_password_line.setPlaceholderText(QCoreApplication.translate("payment", u"#\u65b0\u7684\u5145\u503c\u5bc6\u7801", None))
        self.label_2.setText(QCoreApplication.translate("payment", u"\u7528\u6237\u7f16\u53f7", None))
        self.count_code_line.setText(QCoreApplication.translate("payment", u"000010", None))
        self.count_code_line.setPlaceholderText(QCoreApplication.translate("payment", u"#\u7528\u6237\u7f16\u53f7", None))
        self.label_3.setText(QCoreApplication.translate("payment", u"\u8d2d\u7535\u91d1\u989d", None))
        self.pay_much_line.setText(QCoreApplication.translate("payment", u"10", None))
        self.pay_much_line.setPlaceholderText(QCoreApplication.translate("payment", u"#\u8d2d\u7535\u91d1\u989d", None))
        self.label_4.setText(QCoreApplication.translate("payment", u"\u8d2d\u7535\u6b21\u6570", None))
        self.pay_count_line.setText(QCoreApplication.translate("payment", u"1", None))
        self.pay_count_line.setPlaceholderText(QCoreApplication.translate("payment", u"#\u8d2d\u7535\u6b21\u6570", None))
        self.verify_btn.setText(QCoreApplication.translate("payment", u"1.\u8eab\u4efd\u8ba4\u8bc1", None))
        self.textEdit.setHtml(QCoreApplication.translate("payment", u"<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"hr { height: 1px; border-width: 0; }\n"
"li.unchecked::marker { content: \"\\2610\"; }\n"
"li.checked::marker { content: \"\\2612\"; }\n"
"</style></head><body style=\" font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-size:11pt; color:#ff0000;\">1.\u5148\u70b9\u51fb1\u8eab\u4efd\u8ba4\u8bc1\uff0c\u518d\u8fdb\u884c6\u67e5\u8be2\u72b6\u6001\uff0c\u67e5\u770bdebug\uff0c\u6ca1\u5f00\u6237\u5148\u8fdb\u884c\u5f00\u6237</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-size:11pt; color:#"
                        "ff0000;\">2.\u7b2c\u4e00\u6b21\u5f00\u6237\uff0c\u4f7f\u7528\u9ed8\u8ba4\u7684\u5bc6\u7801\u548c\u8d2d\u7535\u6b21\u6570\u4e0d\u8981\u4fee\u6539\uff08\u8d2d\u7535\u6b21\u6570\u5fc5\u987b\u662f1\uff09\uff0c\u7528\u6237\u7f16\u53f7\u548c\u8d2d\u7535\u91d1\u989d\u53ef\u4ee5\u4fee\u6539\uff0c\u5148\u70b9\u51fb1\uff0c\u540e\u70b9\u51fb3</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-size:11pt; color:#ff0000;\">3.4\u5145\u503c\u30015\u4fee\u6539\u5bc6\u7801\u30016\u67e5\u8be2\u72b6\u6001</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-size:11pt; color:#ff0000;\">4.\u67e5\u8be2\u72b6\u6001\u8bfb\u51fa\u6765\u7684\u8d2d\u7535\u6b21\u6570\u4e3a\u5b9e\u9645\u8d2d\u7535\u6b21\u6570\uff0c\u70b9\u51fb4\u5145\u503c\u4f1a\u81ea\u52a8\u52a01\u65e0\u9700\u624b\u52a8\u4fee\u6539\uff0c\u67e5\u8be2\u72b6\u6001\u540e\u81ea"
                        "\u52a8\u66f4\u65b0\u6570\u636e\u6846</span></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-size:11pt; color:#ff0000;\">5.\u4fee\u6539\u5bc6\u7801\u9700\u8981\u6dfb\u52a0\u65b0\u7684\u5145\u503c\u5bc6\u7801</span></p>\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-size:11pt; color:#ff0000;\"><br /></p></body></html>", None))
        self.dis_verify_btn.setText(QCoreApplication.translate("payment", u"2.\u53d6\u6d88\u8ba4\u8bc1", None))
        self.open_account.setText(QCoreApplication.translate("payment", u"3.\u5f00\u6237", None))
        self.purchase_btn.setText(QCoreApplication.translate("payment", u"4.\u5145\u503c", None))
        self.changed_password_btn.setText(QCoreApplication.translate("payment", u"5.\u4fee\u6539\u5bc6\u7801", None))
        self.check_status_btn.setText(QCoreApplication.translate("payment", u"6.\u67e5\u8be2\u72b6\u6001", None))
        self.close_account_btn.setText(QCoreApplication.translate("payment", u"7.\u9500\u6237", None))
        self.refund_btn.setText(QCoreApplication.translate("payment", u"8.\u9000\u8d39", None))
    # retranslateUi

