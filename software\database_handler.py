import sqlite3
from PySide6.QtWidgets import <PERSON>Widget, QFileDialog, QTableWidgetItem, QTableWidget
from PySide6.QtCore import Qt
from ui文件.database_viewer_ui import Ui_database_viewer

class DatabaseHandler(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.ui = Ui_database_viewer()
        self.ui.setupUi(self)

        # Connect signals to slots
        self.ui.open_db_btn.clicked.connect(self.open_database_file)
        self.ui.execute_query_btn.clicked.connect(self.execute_query)
        self.ui.table_selector_combo.currentTextChanged.connect(self.display_table_data)

        self.db_connection = None

    def open_database_file(self):
        """Opens a dialog to select a SQLite database file."""
        pass

    def execute_query(self):
        """Executes the SQL query from the text edit."""
        pass

    def display_table_data(self, table_name):
        """Displays the data of the selected table."""
        pass

    def closeEvent(self, event):
        """Ensures the database connection is closed when the widget is closed."""
        if self.db_connection:
            self.db_connection.close()
        super().closeEvent(event)
