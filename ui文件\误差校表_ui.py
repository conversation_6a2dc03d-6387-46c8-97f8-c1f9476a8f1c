# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file '误差校表.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ursor,
    QF<PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QGridLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QSizePolicy,
    QSpacerItem, QVBoxLayout, QWidget)

class Ui_err_calibration(object):
    def setupUi(self, err_calibration):
        if not err_calibration.objectName():
            err_calibration.setObjectName(u"err_calibration")
        err_calibration.resize(1269, 250)
        self.verticalLayout = QVBoxLayout(err_calibration)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.gridLayout = QGridLayout()
        self.gridLayout.setSpacing(5)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setContentsMargins(5, 5, 5, 5)
        self.label_11 = QLabel(err_calibration)
        self.label_11.setObjectName(u"label_11")
        self.label_11.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_11, 0, 0, 1, 1)

        self.label_9 = QLabel(err_calibration)
        self.label_9.setObjectName(u"label_9")
        self.label_9.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_9, 0, 1, 1, 1)

        self.label_4 = QLabel(err_calibration)
        self.label_4.setObjectName(u"label_4")
        self.label_4.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_4, 0, 2, 1, 1)

        self.label_5 = QLabel(err_calibration)
        self.label_5.setObjectName(u"label_5")
        self.label_5.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_5, 0, 3, 1, 1)

        self.label_6 = QLabel(err_calibration)
        self.label_6.setObjectName(u"label_6")
        self.label_6.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_6, 0, 4, 1, 1)

        self.label_7 = QLabel(err_calibration)
        self.label_7.setObjectName(u"label_7")
        self.label_7.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_7, 0, 5, 1, 1)

        self.label_8 = QLabel(err_calibration)
        self.label_8.setObjectName(u"label_8")
        self.label_8.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_8, 0, 6, 1, 1)

        self.label_10 = QLabel(err_calibration)
        self.label_10.setObjectName(u"label_10")
        self.label_10.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_10, 0, 7, 1, 1)

        self.label_3 = QLabel(err_calibration)
        self.label_3.setObjectName(u"label_3")
        self.label_3.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_3, 1, 0, 1, 1)

        self.lineEdit = QLineEdit(err_calibration)
        self.lineEdit.setObjectName(u"lineEdit")

        self.gridLayout.addWidget(self.lineEdit, 1, 1, 1, 1)

        self.lineEdit_2 = QLineEdit(err_calibration)
        self.lineEdit_2.setObjectName(u"lineEdit_2")

        self.gridLayout.addWidget(self.lineEdit_2, 1, 2, 1, 1)

        self.lineEdit_3 = QLineEdit(err_calibration)
        self.lineEdit_3.setObjectName(u"lineEdit_3")

        self.gridLayout.addWidget(self.lineEdit_3, 1, 3, 1, 1)

        self.lineEdit_8 = QLineEdit(err_calibration)
        self.lineEdit_8.setObjectName(u"lineEdit_8")

        self.gridLayout.addWidget(self.lineEdit_8, 1, 4, 1, 1)

        self.lineEdit_4 = QLineEdit(err_calibration)
        self.lineEdit_4.setObjectName(u"lineEdit_4")

        self.gridLayout.addWidget(self.lineEdit_4, 1, 5, 1, 1)

        self.lineEdit_6 = QLineEdit(err_calibration)
        self.lineEdit_6.setObjectName(u"lineEdit_6")

        self.gridLayout.addWidget(self.lineEdit_6, 1, 6, 1, 1)

        self.lineEdit_21 = QLineEdit(err_calibration)
        self.lineEdit_21.setObjectName(u"lineEdit_21")

        self.gridLayout.addWidget(self.lineEdit_21, 1, 7, 1, 1)

        self.label_2 = QLabel(err_calibration)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label_2, 2, 0, 1, 1)

        self.lineEdit_13 = QLineEdit(err_calibration)
        self.lineEdit_13.setObjectName(u"lineEdit_13")

        self.gridLayout.addWidget(self.lineEdit_13, 2, 1, 1, 1)

        self.lineEdit_7 = QLineEdit(err_calibration)
        self.lineEdit_7.setObjectName(u"lineEdit_7")

        self.gridLayout.addWidget(self.lineEdit_7, 2, 2, 1, 1)

        self.lineEdit_9 = QLineEdit(err_calibration)
        self.lineEdit_9.setObjectName(u"lineEdit_9")

        self.gridLayout.addWidget(self.lineEdit_9, 2, 3, 1, 1)

        self.lineEdit_11 = QLineEdit(err_calibration)
        self.lineEdit_11.setObjectName(u"lineEdit_11")

        self.gridLayout.addWidget(self.lineEdit_11, 2, 4, 1, 1)

        self.lineEdit_12 = QLineEdit(err_calibration)
        self.lineEdit_12.setObjectName(u"lineEdit_12")

        self.gridLayout.addWidget(self.lineEdit_12, 2, 5, 1, 1)

        self.lineEdit_5 = QLineEdit(err_calibration)
        self.lineEdit_5.setObjectName(u"lineEdit_5")

        self.gridLayout.addWidget(self.lineEdit_5, 2, 6, 1, 1)

        self.lineEdit_19 = QLineEdit(err_calibration)
        self.lineEdit_19.setObjectName(u"lineEdit_19")

        self.gridLayout.addWidget(self.lineEdit_19, 2, 7, 1, 1)

        self.label = QLabel(err_calibration)
        self.label.setObjectName(u"label")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.gridLayout.addWidget(self.label, 3, 0, 1, 1)

        self.lineEdit_17 = QLineEdit(err_calibration)
        self.lineEdit_17.setObjectName(u"lineEdit_17")

        self.gridLayout.addWidget(self.lineEdit_17, 3, 1, 1, 1)

        self.lineEdit_18 = QLineEdit(err_calibration)
        self.lineEdit_18.setObjectName(u"lineEdit_18")

        self.gridLayout.addWidget(self.lineEdit_18, 3, 2, 1, 1)

        self.lineEdit_14 = QLineEdit(err_calibration)
        self.lineEdit_14.setObjectName(u"lineEdit_14")

        self.gridLayout.addWidget(self.lineEdit_14, 3, 3, 1, 1)

        self.lineEdit_15 = QLineEdit(err_calibration)
        self.lineEdit_15.setObjectName(u"lineEdit_15")

        self.gridLayout.addWidget(self.lineEdit_15, 3, 4, 1, 1)

        self.lineEdit_16 = QLineEdit(err_calibration)
        self.lineEdit_16.setObjectName(u"lineEdit_16")

        self.gridLayout.addWidget(self.lineEdit_16, 3, 5, 1, 1)

        self.lineEdit_10 = QLineEdit(err_calibration)
        self.lineEdit_10.setObjectName(u"lineEdit_10")

        self.gridLayout.addWidget(self.lineEdit_10, 3, 6, 1, 1)

        self.lineEdit_20 = QLineEdit(err_calibration)
        self.lineEdit_20.setObjectName(u"lineEdit_20")

        self.gridLayout.addWidget(self.lineEdit_20, 3, 7, 1, 1)


        self.verticalLayout.addLayout(self.gridLayout)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(-1, -1, 6, 6)
        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_3)

        self.one_three_type_box = QComboBox(err_calibration)
        self.one_three_type_box.addItem("")
        self.one_three_type_box.addItem("")
        self.one_three_type_box.addItem("")
        self.one_three_type_box.setObjectName(u"one_three_type_box")

        self.horizontalLayout.addWidget(self.one_three_type_box)

        self.cali_data_box_2 = QComboBox(err_calibration)
        self.cali_data_box_2.addItem("")
        self.cali_data_box_2.addItem("")
        self.cali_data_box_2.addItem("")
        self.cali_data_box_2.setObjectName(u"cali_data_box_2")

        self.horizontalLayout.addWidget(self.cali_data_box_2)

        self.cali_data_box = QComboBox(err_calibration)
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.addItem("")
        self.cali_data_box.setObjectName(u"cali_data_box")

        self.horizontalLayout.addWidget(self.cali_data_box)

        self.pushButton_3 = QPushButton(err_calibration)
        self.pushButton_3.setObjectName(u"pushButton_3")

        self.horizontalLayout.addWidget(self.pushButton_3)


        self.verticalLayout.addLayout(self.horizontalLayout)

        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout.addItem(self.verticalSpacer)


        self.retranslateUi(err_calibration)

        QMetaObject.connectSlotsByName(err_calibration)
    # setupUi

    def retranslateUi(self, err_calibration):
        err_calibration.setWindowTitle(QCoreApplication.translate("err_calibration", u"\u8bef\u5dee\u6821\u8868", None))
        self.label_11.setText(QCoreApplication.translate("err_calibration", u"\u7c7b\u522b", None))
        self.label_9.setText(QCoreApplication.translate("err_calibration", u"\u7535\u538bV", None))
        self.label_4.setText(QCoreApplication.translate("err_calibration", u"\u7535\u6d41A", None))
        self.label_5.setText(QCoreApplication.translate("err_calibration", u"\u6709\u529f\u529f\u7387W", None))
        self.label_6.setText(QCoreApplication.translate("err_calibration", u"\u65e0\u529f\u529f\u7387var", None))
        self.label_7.setText(QCoreApplication.translate("err_calibration", u"\u89c6\u5728\u529f\u7387VA", None))
        self.label_8.setText(QCoreApplication.translate("err_calibration", u"\u96f6\u7ebf\u7535\u6d41A", None))
        self.label_10.setText(QCoreApplication.translate("err_calibration", u"\u8bef\u5dee%", None))
        self.label_3.setText(QCoreApplication.translate("err_calibration", u"A", None))
        self.label_2.setText(QCoreApplication.translate("err_calibration", u"B", None))
        self.label.setText(QCoreApplication.translate("err_calibration", u"C", None))
        self.one_three_type_box.setItemText(0, QCoreApplication.translate("err_calibration", u"\u5355\u76f8\u6821\u8868L", None))
        self.one_three_type_box.setItemText(1, QCoreApplication.translate("err_calibration", u"\u5355\u76f8\u6821\u8868N", None))
        self.one_three_type_box.setItemText(2, QCoreApplication.translate("err_calibration", u"\u4e09\u76f8\u6821\u8868", None))

        self.cali_data_box_2.setItemText(0, QCoreApplication.translate("err_calibration", u"A", None))
        self.cali_data_box_2.setItemText(1, QCoreApplication.translate("err_calibration", u"B", None))
        self.cali_data_box_2.setItemText(2, QCoreApplication.translate("err_calibration", u"C", None))

        self.cali_data_box_2.setCurrentText(QCoreApplication.translate("err_calibration", u"A", None))
        self.cali_data_box.setItemText(0, QCoreApplication.translate("err_calibration", u"\u529f\u7387\u589e\u76ca\u6821\u51c6", None))
        self.cali_data_box.setItemText(1, QCoreApplication.translate("err_calibration", u"\u76f8\u4f4d\u6821\u51c61", None))
        self.cali_data_box.setItemText(2, QCoreApplication.translate("err_calibration", u"\u76f8\u4f4d\u6821\u51c62", None))
        self.cali_data_box.setItemText(3, QCoreApplication.translate("err_calibration", u"\u76f8\u4f4d\u6821\u51c63", None))
        self.cali_data_box.setItemText(4, QCoreApplication.translate("err_calibration", u"\u529f\u7387\u504f\u79fb\u6821\u51c6", None))
        self.cali_data_box.setItemText(5, QCoreApplication.translate("err_calibration", u"\u7535\u6d41\u504f\u79fb\u6821\u51c6", None))
        self.cali_data_box.setItemText(6, QCoreApplication.translate("err_calibration", u"\u5c0f\u4fe1\u53f7\u6821\u51c6", None))
        self.cali_data_box.setItemText(7, QCoreApplication.translate("err_calibration", u"\u5176\u4ed6\u70b9\u6821\u51c6", None))

        self.cali_data_box.setCurrentText(QCoreApplication.translate("err_calibration", u"\u529f\u7387\u589e\u76ca\u6821\u51c6", None))
        self.pushButton_3.setText(QCoreApplication.translate("err_calibration", u"\u6267\u884c", None))
    # retranslateUi

