from typing import TYPE_CHECKING
from PySide6.QtCore import QObject, Signal, Slot, QByteArray, QTimer
from PySide6.QtSerialPort import QSerialPort
from software.protocol import DLT645_2007_s

if TYPE_CHECKING:
    # 仅在类型检查时导入，避免运行时的循环导入问题
    pass

class SerialWorker(QObject):
    """
    一个纯粹的串口工作器，在独立的线程中运行。
    它负责所有底层的串口I/O和字节流解析。
    它不了解任何UI界面，只通过信号与外界通信。
    """

    frame_decoded = Signal(dict)  # 解析出完整数据帧
    error_occurred = Signal(str)  # 发生错误
    log_message = Signal(str, str)  # 日志消息

    def __init__(self, protocol_handler: DLT645_2007_s):
        super().__init__()
        self.protocol_handler = protocol_handler
        self.serial_port = QSerialPort()
        self.receive_buffer = bytearray()
        self._is_port_open = False
        self.baud_rate = 9600

        # 创建一个定时器来处理接收超时
        self.receive_timer = QTimer()
        self.receive_timer.setSingleShot(True)
        self.receive_timer.timeout.connect(self._on_receive_timeout)

    @Slot(dict)  # type: ignore
    def start_port(self, port_config: dict):
        if self._is_port_open:
            self.log_message.emit("端口已经打开，请先关闭。", "orange")
            return

        try:
            self.serial_port.setPortName(port_config.get("port_name", ""))
            self.baud_rate = port_config.get("baud_rate", 9600)
            self.serial_port.setBaudRate(self.baud_rate)

            # --- 完整地设置数据位、校验位、停止位 ---
            data_bits_map = {"8位": QSerialPort.DataBits.Data8, "7位": QSerialPort.DataBits.Data7}
            data_bits = data_bits_map.get(port_config.get("data_bits_str", "8位"), QSerialPort.DataBits.Data8)
            self.serial_port.setDataBits(data_bits)

            parity_map = {
                "无校验": QSerialPort.Parity.NoParity,
                "奇校验": QSerialPort.Parity.OddParity,
                "偶校验": QSerialPort.Parity.EvenParity,
            }
            parity = parity_map.get(port_config.get("parity_str", "无校验"), QSerialPort.Parity.NoParity)
            self.serial_port.setParity(parity)

            stop_bits_map = {"1": QSerialPort.StopBits.OneStop, "2": QSerialPort.StopBits.TwoStop}
            stop_bits = stop_bits_map.get(port_config.get("stop_bits_str", "1"), QSerialPort.StopBits.OneStop)
            self.serial_port.setStopBits(stop_bits)
            # --- 设置结束 ---

            # 设置固定的帧间超时
            timeout = 50  # ms
            self.receive_timer.setInterval(timeout)

            if self.serial_port.open(QSerialPort.OpenModeFlag.ReadWrite):
                self._is_port_open = True
                self.serial_port.readyRead.connect(self.on_ready_read)
                self.log_message.emit(f"端口 {port_config.get('port_name')} 已打开，帧超时设置为 {timeout}ms。", "green")
            else:
                self.error_occurred.emit(f"无法打开串口: {self.serial_port.errorString()}")
        except Exception as e:
            self.error_occurred.emit(f"打开端口时发生意外错误: {e}")

    @Slot()
    def stop_port(self):
        if self._is_port_open:
            self.serial_port.close()
            self._is_port_open = False
            self.log_message.emit("端口已关闭。", "black")

    @Slot(QByteArray)  # type: ignore
    def send_data(self, data: bytes):
        if self._is_port_open:
            self.serial_port.write(data)
        else:
            self.error_occurred.emit("发送失败：端口未打开。")

    @Slot()
    def on_ready_read(self):
        if not self._is_port_open:
            return

        new_data = self.serial_port.readAll().data()
        if not new_data:
            return

        self.receive_buffer.extend(new_data)
        # 重启帧间定时器
        self.receive_timer.start()

    @Slot()
    def _on_receive_timeout(self):

        if not self.receive_buffer:
            return

        try:
            # 将整个缓冲区作为一个潜在的数据帧进行处理
            potential_frame = bytes(self.receive_buffer)
            self.log_message.emit(f"处理数据: {' '.join(f'{b:02X}' for b in potential_frame)}", "gray")
            
            decoded = self.protocol_handler.dlt645_parse_response(potential_frame)
            
            if isinstance(decoded, dict):
                self.frame_decoded.emit(decoded)
            elif isinstance(decoded, str):
                self.log_message.emit(f"协议解析消息: {decoded}", "orange")

        except Exception as e:
            self.error_occurred.emit(f"协议解析时发生错误: {e}")
        finally:
            # 无论成功与否，清空缓冲区以准备接收下一帧
            self.receive_buffer.clear()
