import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

import os
from PySide6.QtWidgets import QWidget
from ui文件.充值页面_ui import Ui_payment # 导入充值页面UI类 # noqa: E402

class PaymentWindow(QWidget, Ui_payment):
    def __init__(self, main_window):
        super().__init__()
        self.setupUi(self)
        self.main_window = main_window

        self.verify_btn.clicked.connect(self.verify)
        self.open_account.clicked.connect(self.open_account_func)
        self.purchase_btn.clicked.connect(self.purchase)
        self.changed_password_btn.clicked.connect(self.change_password)
        self.check_status_btn.clicked.connect(self.check_status)
        self.close_account_btn.clicked.connect(self.close_account)
        self.refund_btn.clicked.connect(self.refund)

    def verify(self):
        # 1. Get password
        password_str = self.password_line.text()
        if len(password_str) != 8:
            self.main_window.serial_handler.write_data_to_log_edit("密码长度应为8位", "red")
            return
        try:
            # Assuming the password on the UI is a hex string of 8 characters (4 bytes)
            password_bytes = bytes.fromhex(password_str)
            password_bytes = password_bytes[::-1] # Swap byte order
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("密码格式错误，应为8位十六进制数", "red")
            return

        # 2. Generate random number
        random_bytes = os.urandom(2)

        # 3. Expiration time
        expiration_bytes = b'\xff\xff'

        # 4. Construct data for CRC calculation
        data_for_crc = password_bytes + random_bytes + expiration_bytes

        # 5. Calculate CRC
        crc_bytes = self.crc16_modbus(data_for_crc)

        # 6. Construct the full data payload
        data_id = b'\xff\x00\x00\x07'
        operator_code = b'\x00\x00\x00\x00'
        data = data_id + operator_code + data_for_crc + crc_bytes

        # 7. Call handle_payment_command
        self.handle_payment_command(data)

    def open_account_func(self):
        # 1. Get password
        password_str = self.password_line.text()
        if len(password_str) != 8:
            self.main_window.serial_handler.write_data_to_log_edit("密码长度应为8位", "red")
            return
        try:
            password_bytes = bytes.fromhex(password_str)[::-1]
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("密码格式错误，应为8位十六进制数", "red")
            return

        # 2. Generate random numbers
        random_bytes1 = os.urandom(2)
        random_bytes2 = os.urandom(2)

        # 3. Get purchase amount
        amount_str = self.pay_much_line.text()
        try:
            amount_float = float(amount_str)
            amount_int = int(amount_float * 100) # Convert to cents
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("购电金额格式错误，应为数字", "red")
            return
        amount_bytes = amount_int.to_bytes(4, byteorder='little')

        # 4. Get purchase count
        self.pay_count_line.setText("1")
        try:
            count_int = 1
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("购电次数格式错误，应为数字", "red")
            return
        count_bytes = count_int.to_bytes(4, byteorder='little')

        # 5. Get user number
        user_no_str = self.count_code_line.text()
        if len(user_no_str) > 8:
            self.main_window.serial_handler.write_data_to_log_edit("用户编号长度不能超过8位", "red")
            return
        try:
            user_no_bytes = bytes.fromhex(user_no_str.zfill(8))[::-1]
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("用户编号格式错误，应为十六进制数", "red")
            return

        # 6. Construct data for CRC
        data_for_crc = password_bytes + random_bytes1 + random_bytes2 + amount_bytes + count_bytes + user_no_bytes

        # 7. Calculate CRC
        crc_bytes = self.crc16_modbus(data_for_crc)

        # 8. Construct full data payload
        data_id = b'\xff\x01\x01\x07'
        operator_code = b'\x00\x00\x00\x00'
        data = data_id + operator_code + data_for_crc + crc_bytes

        # 9. Call handle_payment_command
        self.handle_payment_command(data)

    def purchase(self):
        # 1. Get password
        password_str = self.password_line.text()
        if len(password_str) != 8:
            self.main_window.serial_handler.write_data_to_log_edit("密码长度应为8位", "red")
            return
        try:
            password_bytes = bytes.fromhex(password_str)[::-1]
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("密码格式错误，应为8位十六进制数", "red")
            return

        # 2. Generate random numbers
        random_bytes1 = os.urandom(2)
        random_bytes2 = os.urandom(2)

        # 3. Get purchase amount
        amount_str = self.pay_much_line.text()
        try:
            amount_float = float(amount_str)
            amount_int = int(amount_float * 100) # Convert to cents
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("购电金额格式错误，应为数字", "red")
            return
        amount_bytes = amount_int.to_bytes(4, byteorder='little')

        # 4. Get purchase count and increment by 1
        count_str = self.pay_count_line.text()
        try:
            count_int = int(count_str) + 1
            self.pay_count_line.setText(str(count_int))
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("购电次数格式错误，应为数字", "red")
            return
        count_bytes = count_int.to_bytes(4, byteorder='little')

        # 5. Get user number
        user_no_str = self.count_code_line.text()
        if len(user_no_str) > 8:
            self.main_window.serial_handler.write_data_to_log_edit("用户编号长度不能超过8位", "red")
            return
        try:
            user_no_bytes = bytes.fromhex(user_no_str.zfill(8))[::-1]
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("用户编号格式错误，应为十六进制数", "red")
            return

        # 6. Construct data for CRC
        data_for_crc = password_bytes + random_bytes1 + random_bytes2 + amount_bytes + count_bytes + user_no_bytes

        # 7. Calculate CRC
        crc_bytes = self.crc16_modbus(data_for_crc)

        # 8. Construct full data payload
        data_id = b'\xff\x02\x01\x07'
        operator_code = b'\x00\x00\x00\x00'
        data = data_id + operator_code + data_for_crc + crc_bytes

        # 9. Call handle_payment_command
        self.handle_payment_command(data)

    def change_password(self):
        pass

    def check_status(self):
        # 1. Get password
        password_str = self.password_line.text()
        if len(password_str) != 8:
            self.main_window.serial_handler.write_data_to_log_edit("密码长度应为8位", "red")
            return
        try:
            password_bytes = bytes.fromhex(password_str)[::-1]
        except ValueError:
            self.main_window.serial_handler.write_data_to_log_edit("密码格式错误，应为8位十六进制数", "red")
            return

        # 2. Generate random numbers
        random_bytes1 = os.urandom(2)
        random_bytes2 = os.urandom(2)

        # 3. Construct data for CRC
        data_for_crc = password_bytes + random_bytes1 + random_bytes2

        # 4. Calculate CRC
        crc_bytes = self.crc16_modbus(data_for_crc)

        # 5. Construct full data payload
        data_id = b'\xff\x02\x81\x07'
        operator_code = b'\x00\x00\x00\x00'
        data = data_id + operator_code + data_for_crc + crc_bytes

        # 6. Call handle_payment_command
        self.handle_payment_command(data)

    def close_account(self):
        pass

    def refund(self):
        pass

    def handle_payment_command(self, data):
        cmd_code = 0x03
        addr_text = self.main_window.addr_edit.text().strip()
        if not all(c in "0123456789aA" for c in addr_text) or len(addr_text) != 12:
            self.main_window.serial_handler.write_data_to_log_edit("地址格式错误：必须是12位十六进制字符", "red")
            return

        addr_bytes = bytearray()
        for i in range(0, 12, 2):
            hex_pair = addr_text[i : i + 2]
            byte_val = int(hex_pair, 16)
            addr_bytes.append(byte_val)
        addr_bytes.reverse()
        addr = bytes(addr_bytes)

        params = {"addr": addr, "cmd_code": cmd_code, "data": data}
        cmd_frame = self.main_window.protocol_handler.dlt645_build_cmd(params)

        if cmd_frame:
            self.main_window.serial_handler.write_data_to_log_edit(f"发送: {' '.join(f'{x:02X}' for x in cmd_frame)}", "blue")
            self.main_window.serial_handler.send_data_command.emit(cmd_frame)

    def crc16_modbus(self, data: bytes) -> bytes:
        crc = 0xFFFF
        for pos in data:
            crc ^= pos
            for i in range(8):
                if (crc & 1) != 0:
                    crc >>= 1
                    crc ^= 0xA001
                else:
                    crc >>= 1
        return crc.to_bytes(2, byteorder='little')

    def parse_status_response(self, data: bytes):
        # The data from the DLT645 frame needs to be subtracted by 0x33
        processed_data = bytes([(b - 0x33) & 0xFF for b in data])

        data_id = processed_data[0:4]
        if data_id != b'\xff\x02\x81\x07':
            return
        
        # Expected data length: 4 (Data ID) + 4 (User Number) + 4 (Purchase Balance) + 4 (Purchase Count) = 16 bytes
        if len(data) < 16:
            self.main_window.serial_handler.write_data_to_log_edit("状态查询响应数据长度不足", "red")
            return
        
        user_number_bytes = processed_data[4:8]
        purchase_balance_bytes = processed_data[8:12]
        purchase_count_bytes = processed_data[12:16]

        # User Number: 99 99 99 99 (hex string, reversed)
        # "未开户状态下用户编号为 FFFFFFFF"
        user_number_hex = user_number_bytes[::-1].hex().upper()
        if user_number_hex == "FFFFFFFF":
            user_number_display = "未开户"
        else:
            user_number_display = user_number_hex

        # Purchase Balance: 12 34 56 78 (little-endian, 785634.12 元)
        purchase_balance_int = int.from_bytes(purchase_balance_bytes, byteorder='little')
        purchase_balance_display = f"{purchase_balance_int / 100:.2f}"

        # Purchase Count: 01 00 00 00 (little-endian, 1 次)
        purchase_count_int = int.from_bytes(purchase_count_bytes, byteorder='little')
        purchase_count_display = str(purchase_count_int)

        # Update UI
        self.count_code_line.setText(user_number_display)
        self.pay_much_line.setText(purchase_balance_display)
        self.pay_count_line.setText(purchase_count_display)

        self.main_window.serial_handler.write_data_to_log_edit("状态查询响应已解析并更新UI", "green")
