from datetime import datetime
from PySide6.QtWidgets import QMessageBox
from PySide6.QtSerialPort import QSerialPortInfo
from PySide6.QtCore import QObject, Slot, QThread, Signal

# 导入类型检查模块，用于解决循环导入问题
from typing import TYPE_CHECKING

# 导入我们新创建的 Worker 和协议处理器
from software.tasks.serial_worker import SerialWorker
from software.protocol import DLT645_2007_s

# 这个代码块只在类型检查时执行，运行时不执行
# 这样可以安全地导入 MainApp 类型用于提示，而不会导致循环导入错误
if TYPE_CHECKING:
    from main import MainWindow


class serial_fuc_s(QObject):
    """串口功能的协调者 (Coordinator)

    这个类运行在主线程，作为UI和后台串口工作线程之间的桥梁。
    """

    # ===== 定义向后台 Worker 下达指令的信号 =====
    start_port_command = Signal(dict)
    stop_port_command = Signal()
    send_data_command = Signal(bytes)

    def __init__(self, main_window: "MainWindow") -> None:
        """初始化协调者和后台工作线程"""
        super().__init__()
        if main_window is None:
            raise ValueError("main_window cannot be None")
        self.main_window = main_window
        self._is_port_open = False

        # 1. 创建协议处理器实例
        self.protocol_handler = DLT645_2007_s(self.main_window)

        # 2. 创建 Worker 和 Thread
        self.worker_thread = QThread()
        self.worker = SerialWorker(self.protocol_handler)
        self.worker.moveToThread(self.worker_thread)
        # 显式地将定时器移动到工作线程，以确保其线程关联性
        self.worker.receive_timer.moveToThread(self.worker_thread)

        # 3. 连接“协调者”的指令信号到 Worker 的槽函数
        self.start_port_command.connect(self.worker.start_port)
        self.stop_port_command.connect(self.worker.stop_port)
        self.send_data_command.connect(self.worker.send_data)

        # 4. 连接 Worker 的结果信号到“协调者”的处理槽函数 (使用 lambda 包装)
        self.worker.frame_decoded.connect(lambda frame: self.on_frame_decoded(frame))
        self.worker.error_occurred.connect(lambda err_msg: self.on_worker_error(err_msg))
        self.worker.log_message.connect(lambda msg, color: self.write_data_to_log_edit(msg, color))

        # 5. 连接线程的生命周期信号
        self.worker_thread.started.connect(lambda: self.write_data_to_log_edit("后台串口线程已启动。", "gray"))
        self.worker_thread.finished.connect(self.worker.deleteLater)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)

        # 6. 启动线程
        self.worker_thread.start()

        # 7. 连接UI控件的信号到“协调者”的槽函数
        self.main_window.refresh_btn.clicked.connect(self.refresh_serial)
        self.main_window.open_btn.clicked.connect(self.handle_serial_connection)
        self.main_window.set_serial_1_btn.clicked.connect(self.set_serial_115200)
        self.main_window.set_serial_2_btn.clicked.connect(self.set_serial_2400)

        # 初始刷新串口列表
        self.refresh_serial()

    @Slot(dict) # type: ignore
    def on_frame_decoded(self, decoded_frame: dict):
        """处理后台 Worker 解析出的完整数据帧"""
        try:
            cmd_code = decoded_frame.get("cmd_code", 0)
            data_bytes = decoded_frame.get("data", b'')
            data_hex = ' '.join(f'{b:02X}' for b in data_bytes)            
            
            # Check if it's a status response
            if cmd_code == 0x83:
                self.main_window.payment_window.parse_status_response(data_bytes)

            # 根据DLT645的控制码定义来判断响应类型
            if (cmd_code & 0xC0) == 0xC0:  # 错误响应
                self.write_data_to_log_edit(f"收到错误响应: C_CODE={cmd_code:02X}, DATA=[{data_hex}]", "red")
            elif (cmd_code & 0x80) == 0x80:  # 正确响应
                self.write_data_to_log_edit(f"收到正确响应: C_CODE={cmd_code:02X}, DATA=[{data_hex}]", "green")
            else: # 未知或非标准响应
                self.write_data_to_log_edit(f"收到未知响应: C_CODE={cmd_code:02X}, DATA=[{data_hex}]", "orange")

        except Exception as e:
            self.write_data_to_log_edit(f"处理回帧时出错: {e}", "red")

    @Slot(str) # type: ignore
    def on_worker_error(self, error_message: str):
        """处理后台 Worker 发来的错误信息"""
        self.write_data_to_log_edit(error_message, "red")
        # QMessageBox.critical(self.main_window, "错误", error_message, QMessageBox.StandardButton.Ok, QMessageBox.StandardButton.Ok)
        
        if any(keyword in error_message for keyword in ["端口", "串口", "失败"]):
            self.main_window.open_btn.setText("打开串口")
            self.enable_serial_controls()
            self._is_port_open = False

    def send_response(self, response: bytes) -> None:
        """发送响应数据 (现在通过发射信号来完成)"""
        log_str = "".join([f"{x:02X} " for x in response])
        self.write_data_to_log_edit(f"发送: {log_str}", "blue")
        self.send_data_command.emit(response)

    def handle_serial_connection(self) -> None:
        """处理“打开/关闭串口”按钮的点击事件"""
        if not self._is_port_open:
            self.disable_serial_controls()
            port_name = self.main_window.port_box.currentText()
            if not port_name:
                self.on_worker_error("请选择串口")
                return

            port_config = {
                "port_name": port_name,
                "baud_rate": int(self.main_window.band_rate_box.currentText()),
                "data_bits_str": self.main_window.data_len_box.currentText(),
                "parity_str": self.main_window.even_box.currentText(),
                "stop_bits_str": self.main_window.stop_bits_box.currentText(),
            }
            
            self.start_port_command.emit(port_config)
            self.main_window.open_btn.setText("关闭串口")
            self._is_port_open = True
        else:
            self.stop_port_command.emit()
            self.main_window.open_btn.setText("打开串口")
            self.enable_serial_controls()
            self._is_port_open = False

    def refresh_serial(self) -> None:
        port_list = list(QSerialPortInfo.availablePorts())
        self.main_window.port_box.clear()
        self.main_window.port_box.addItems([port.portName() for port in port_list])

    def set_serial_115200(self) -> None:
        self.main_window.band_rate_box.setCurrentText("115200")
        self.main_window.data_len_box.setCurrentText("8位")
        self.main_window.even_box.setCurrentText("无校验")
        self.main_window.stop_bits_box.setCurrentText("1")

    def set_serial_2400(self) -> None:
        self.main_window.band_rate_box.setCurrentText("2400")
        self.main_window.data_len_box.setCurrentText("8位")
        self.main_window.even_box.setCurrentText("偶校验")
        self.main_window.stop_bits_box.setCurrentText("1")

    def disable_serial_controls(self) -> None:
        self.main_window.port_box.setEnabled(False)
        self.main_window.band_rate_box.setEnabled(False)
        self.main_window.data_len_box.setEnabled(False)
        self.main_window.even_box.setEnabled(False)
        self.main_window.stop_bits_box.setEnabled(False)
        self.main_window.set_serial_1_btn.setEnabled(False)
        self.main_window.set_serial_2_btn.setEnabled(False)
        self.main_window.refresh_btn.setEnabled(False)

    def enable_serial_controls(self) -> None:
        self.main_window.port_box.setEnabled(True)
        self.main_window.band_rate_box.setEnabled(True)
        self.main_window.data_len_box.setEnabled(True)
        self.main_window.even_box.setEnabled(True)
        self.main_window.stop_bits_box.setEnabled(True)
        self.main_window.set_serial_1_btn.setEnabled(True)
        self.main_window.set_serial_2_btn.setEnabled(True)
        self.main_window.refresh_btn.setEnabled(True)

    @Slot(str, str) # type: ignore
    def write_data_to_log_edit(self, data: str, color: str = "green") -> None:
        timestamp = datetime.now().strftime("[%Y-%m-%d %H:%M:%S.%f")[:-3] + "] "
        html_text = (
            f'<div>'
            f'<span style="color: gray; font-family: Consolas;">{timestamp}</span>'
            f'<span style="color: {color}; font-family: Consolas;">{data}</span>'
            f"</div>"
        )
        self.main_window.log_edit.append(html_text)
        self.main_window.log_edit.verticalScrollBar().setValue(self.main_window.log_edit.verticalScrollBar().maximum())

    def __del__(self):
        if hasattr(self, 'worker_thread') and self.worker_thread.isRunning():
            self.stop_port_command.emit()
            self.worker_thread.quit()
            self.worker_thread.wait()