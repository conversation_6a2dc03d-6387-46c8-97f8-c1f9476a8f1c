<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>payment</class>
 <widget class="QWidget" name="payment">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1146</width>
    <height>310</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>充值</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0">
    <layout class="QGridLayout" name="gridLayout">
     <item row="0" column="0">
      <widget class="QLabel" name="label_5">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>充值密码</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1" colspan="2">
      <widget class="QLineEdit" name="password_line">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>19800000</string>
       </property>
       <property name="placeholderText">
        <string>#充值密码</string>
       </property>
      </widget>
     </item>
     <item row="0" column="3">
      <widget class="QLineEdit" name="new_password_line">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="layoutDirection">
        <enum>Qt::LayoutDirection::LeftToRight</enum>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="placeholderText">
        <string>#新的充值密码</string>
       </property>
      </widget>
     </item>
     <item row="0" column="4">
      <widget class="QLabel" name="label_2">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>用户编号</string>
       </property>
      </widget>
     </item>
     <item row="0" column="5">
      <widget class="QLineEdit" name="count_code_line">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>000010</string>
       </property>
       <property name="placeholderText">
        <string>#用户编号</string>
       </property>
      </widget>
     </item>
     <item row="0" column="6">
      <widget class="QLabel" name="label_3">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>购电金额</string>
       </property>
      </widget>
     </item>
     <item row="0" column="7">
      <widget class="QLineEdit" name="pay_much_line">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>10</string>
       </property>
       <property name="placeholderText">
        <string>#购电金额</string>
       </property>
      </widget>
     </item>
     <item row="0" column="8">
      <widget class="QLabel" name="label_4">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>购电次数</string>
       </property>
      </widget>
     </item>
     <item row="0" column="9">
      <widget class="QLineEdit" name="pay_count_line">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>1</string>
       </property>
       <property name="placeholderText">
        <string>#购电次数</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0" colspan="2">
      <widget class="QPushButton" name="verify_btn">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>1.身份认证</string>
       </property>
      </widget>
     </item>
     <item row="1" column="2" rowspan="8" colspan="8">
      <widget class="QTextEdit" name="textEdit">
       <property name="readOnly">
        <bool>true</bool>
       </property>
       <property name="html">
        <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Microsoft YaHei UI'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:11pt; color:#ff0000;&quot;&gt;1.先点击1身份认证，再进行6查询状态，查看debug，没开户先进行开户&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:11pt; color:#ff0000;&quot;&gt;2.第一次开户，使用默认的密码和购电次数不要修改（购电次数必须是1），用户编号和购电金额可以修改，先点击1，后点击3&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:11pt; color:#ff0000;&quot;&gt;3.4充值、5修改密码、6查询状态&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:11pt; color:#ff0000;&quot;&gt;4.查询状态读出来的购电次数为实际购电次数，点击4充值会自动加1无需手动修改，查询状态后自动更新数据框&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:11pt; color:#ff0000;&quot;&gt;5.修改密码需要添加新的充值密码&lt;/span&gt;&lt;/p&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-size:11pt; color:#ff0000;&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0" colspan="2">
      <widget class="QPushButton" name="dis_verify_btn">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>2.取消认证</string>
       </property>
      </widget>
     </item>
     <item row="3" column="0" colspan="2">
      <widget class="QPushButton" name="open_account">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>3.开户</string>
       </property>
      </widget>
     </item>
     <item row="4" column="0" colspan="2">
      <widget class="QPushButton" name="purchase_btn">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>4.充值</string>
       </property>
      </widget>
     </item>
     <item row="5" column="0" colspan="2">
      <widget class="QPushButton" name="changed_password_btn">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>5.修改密码</string>
       </property>
      </widget>
     </item>
     <item row="6" column="0" colspan="2">
      <widget class="QPushButton" name="check_status_btn">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>6.查询状态</string>
       </property>
      </widget>
     </item>
     <item row="7" column="0" colspan="2">
      <widget class="QPushButton" name="close_account_btn">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>7.销户</string>
       </property>
      </widget>
     </item>
     <item row="8" column="0" colspan="2">
      <widget class="QPushButton" name="refund_btn">
       <property name="font">
        <font>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="text">
        <string>8.退费</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
